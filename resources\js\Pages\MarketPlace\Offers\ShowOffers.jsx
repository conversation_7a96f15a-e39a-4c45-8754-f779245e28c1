import React, { useState } from 'react'
import DataTable from 'react-data-table-component';
import OfferHistoryPopup from '../components/OfferHistoryPopup';
import { FaSearch } from "react-icons/fa";
import { PiCurrencyDollarDuotone } from "react-icons/pi";
import { FaBalanceScaleLeft } from "react-icons/fa";
import { ImCross } from "react-icons/im";
import AdminLayout from '@/Layouts/AdminLayout';
import { offerCustomStyles } from '../components/offerCustomStyles';
import { BiEdit } from "react-icons/bi";
import { useEffect } from 'react';

export default function ShowOffers({myoffers}) {

    const [offers, setOffers] = useState([]);
    const [modal, showModal] = useState(false);
    const [domain, setDomain] = useState({name: '', created_at: new Date(), offer_price: 0});

    const columns = [
        {
            id: 'user',
            name: 'User',
            left: "true",
            selector: row => `${row.first_name} ${row.last_name}`,
            sortable: true,
        },
        {
            id: 'domain',
            name: 'Domain',
            left: "true",
            selector: row => row.domain_name,
            sortable: true,
            width: '200px'
        },
        {
            id: 'price',
            name: 'Initial Offer',
            left: "true",
            selector: row => row.offer_price,
            cell: row => `$${row.offer_price}`,
            sortable: true,
            width: '130px'
        },
        {
            id: 'status_change',
            name: 'Last Status Update',
            left: "true",
            selector: row => row.updated_at,
            cell: row => `${new Date(row.updated_at).toLocaleString()}`,
            sortable: true,
            width: '180px'
        },
        {
            id: 'status',
            name: 'Status',
            left: "true",
            selector: row => row.offer_status,
            cell: row => getStatus(row.offer_status),
            sortable: true,
            width: '190px'
        },
        {
            id: 'current',
            name: 'Buy Now Price',
            left: "true",
            selector: row => row.counter_offer_price,
            cell: row => `${row.counter_offer_price > 0 ? `$${row.counter_offer_price}` : 'NA'}`,
            sortable: true,
            width: '150px'
        },
        {
            id: 'action',
            name: 'Action',
            selector: row => getDetailButton(row),
            sortable: true,
        },
    ];

    const handlePopUp = (row) => {
        setDomain(row)
        showModal(true);
    }

    const getDetailButton = (row) => {
        return <div className='flex gap-1 font-bold'>
            <div className='has-tooltip'>
                <span className='tooltip rounded shadow-lg bg-gray-100 text-green-700 px-3 py-1 -mt-8'>Edit</span>
                <button onClick={() => { handlePopUp(row) }} className='bg-green-500 bg-opacity-20 rounded-md font-bold text-lg text-green-700 p-1.5 flex items-center space-x-2'>
                    <BiEdit className=' font-bold' />
                </button>
            </div>

        </div>
    }

    const statusColors = {
        waiting: "bg-gray-500 text-white",
        offer_closed: "bg-red-500 text-white",
        counter_offer: "bg-yellow-500 text-black",
        offer_accepted: "bg-green-500 text-white",
        offer_rejected: "bg-pink-500 text-white",
        user_counter_offer: "bg-orange-500 text-white",

        paid_hold_pending: "bg-blue-500 text-white",
        paid_order_pending: "bg-purple-500 text-white",
        paid_transfer_pending: "bg-teal-500 text-white",
        paid_transfer_requested: "bg-indigo-500 text-white",
        paid_transfer_completed: "bg-lime-500 text-black",
    };

    const getStatus = (status) => {
        return <span className={`${statusColors[status]} p-1 px-3 capitalize text-xs rounded-full`}>{status.replaceAll('_', ' ').replaceAll('offer', '')}</span>
    }

    const getDiv = (name, price, status) => {
        return <div className='flex flex-col py-3'>
            <div className='text-xl font-medium text-gray-800'>{name}</div>
            <div className='text-[15px] font-medium text-gray-500 flex gap-2 pt-1'>
                <div className='pt-0.5'>${price}</div>
                {getStatus(status)}
            </div>
        </div>
    }

    useEffect(() => {
        setOffers(myoffers)
    }, [])

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[1200px] mt-20 flex flex-col space-y-4">

                <OfferHistoryPopup showModal={showModal} offers={offers} setOffers={setOffers} getStatus={getStatus} modal={modal} domain={domain} />

                <div className="mt-1 ml-5">
                    <DataTable
                        columns={columns}
                        data={offers}
                        pagination
                        persistTableHead
                        highlightOnHover
                        customStyles={offerCustomStyles}
                        pointerOnHover
                        fixedHeader
                        fixedHeaderScrollHeight="600px"
                    />
                </div>
            </div>
        </AdminLayout>
    )
}
