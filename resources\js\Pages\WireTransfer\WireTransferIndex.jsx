//* PACKAGES
import React, { useState, useEffect } from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
import { ImSortAlphaAsc, ImSortAlphaDesc } from "react-icons/im";
import { MdOutlineSettings, MdOutlineFilterAlt } from "react-icons/md";
import { TbSortAscending2, TbSortDescending2 } from "react-icons/tb";

//* COMPONENTS
import AdminLayout from "../../Layouts/AdminLayout";
import Filter from "../../Components/WireTransfer/Filter";
import CursorPaginate from "@/Components/Util/CursorPaginate";
import WireTransferItemComponent from "@/Components/WireTransfer/WireTransferItemComponent";
import WireTransferModalNoteComponent from '@/Components/WireTransfer/WireTransferModalNoteComponent';

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
import { getEventValue } from "@/Util/TargetInputEvent";

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function Index(
    {
        items,
        onFirstPage,
        onLastPage,
        nextPageUrl,
        previousPageUrl,
        itemCount = 0,
        total = 0,
    }
) {
    //! PACKAGE
    //...

    //! HOOKS
    const { hasPermission } = usePermissions();
    const paramOrderBy = route().params.orderby;
    const paramCompany = route().params.company;
    //! VARIABLES
    const SORT_TYPE =
    {
        NAME_ASC: "Name: Asc",
        NAME_DESC: "Name: Desc",
        CREATED_ASC: "Date Created: Asc",
        CREATED_DESC: "Date Created: Desc",
        UPDATED_ASC: "Date Updated: Asc",
        UPDATED_DESC: "Date Updated: Desc",           
    };

    //! STATES
    const [selectedItems, setSelectedItems] = useState([]);
    const [limit, setLimit] = useState(route().params.limit || 10);
    const [stateModalActiveNote, setStateModalActiveNote] = useState(false);
    const [stateSelectedItem, setStateSelectedItem] = useState(null);

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    function handleItemCheckboxChange(itemId, e) {
        if (getEventValue(e)) {
            setSelectedItems((prevSelectedItems) => {
                return [...prevSelectedItems, itemId];
            });
            setSelectAll(() => items.length === selectedItems.length + 1);
        }
        else {
            setSelectedItems((prevSelectedItems) =>
                prevSelectedItems.filter((id) => id !== itemId)
            );
            setSelectAll(false);
        }
    };

    function handleSortOrder(sortOrder) {
        let payload = {};

        payload.orderby = sortOrder;

        router.get(route("billing.wire.transfer"), payload);
    };

    // Handle limit change
    function handleLimitChange(e) {
        const newLimit = getEventValue(e);
        setLimit(newLimit);
        router.get(route("billing.wire.transfer"), {
            ...route().params,
            limit: newLimit,
        }, { preserveState: true });
    };

    return (
        <AdminLayout>
            {/* hideNav={true} */}
            <WireTransferModalNoteComponent
                stateSelectedItem={stateSelectedItem}
                stateIsModalOpen={stateModalActiveNote}
                handleEventModalClose={() => {
                    setStateSelectedItem(null);
                    setStateModalActiveNote(false);
                }
                }
                handleEventModalConfirm={() => {
                    setStateSelectedItem(null);
                    setStateModalActiveNote(false);
                }
                }

            />

            <div className="mx-auto container max-w-[1100px] mt-20 flex flex-col space-y-4">
                <div
                    id="sample"
                    className="flex items-center space-x-2 flex-wrap min-h-[2rem]"
                >
                    <label className="flex items-center">
                        <MdOutlineFilterAlt />
                        <span className="ml-2 text-sm text-gray-600">
                            {" "}
                            Filter:{" "}
                        </span>
                    </label>
                    <Filter />
                </div>

                <div className="flex justify-start">
                    <label className="mr-2 text-sm pt-1 text-gray-600">
                        Show
                    </label>
                    <select
                        value={limit}
                        onChange={handleLimitChange}
                        className="border border-gray-300 rounded px-4 py-1 text-sm w-20"
                    >
                        {[20, 25, 30, 40, 50, 100].map((val) => (
                            <option key={val} value={val}>
                                {val}
                            </option>
                        ))}
                    </select>
                </div>

                <div>
                    <table className="min-w-[1200px] text-left border-spacing-y-2.5 border-separate">
                        <thead className=" bg-gray-50 text-sm">
                            <tr>
                                <th className="py-3">
                                    <label className="flex items-center pl-2 space-x-2">
                                        <span>Name</span>
                                        <button
                                            onClick={() =>
                                                handleSortOrder(
                                                    paramOrderBy ===
                                                        SORT_TYPE.NAME_ASC
                                                        ? SORT_TYPE.NAME_DESC
                                                        : SORT_TYPE.NAME_ASC
                                                )
                                            }
                                            disabled={items.length === 0}
                                        >
                                            {paramOrderBy ===
                                                SORT_TYPE.NAME_ASC ? (
                                                <ImSortAlphaAsc />
                                            ) : (
                                                <ImSortAlphaDesc />
                                            )}
                                        </button>
                                    </label>
                                </th>
                                <th className="">
                                    <span>Company</span>
                                </th>
                                <th className="">
                                    <span>Amount</span>
                                </th>
                                <th className="">
                                    <span>Purpose</span>
                                </th>
                                <th className="">
                                    <span>Status</span>
                                </th>
                                <th className="">
                                    <span>Note</span>
                                </th>
                                <th className="">
                                    <label className="flex items-center space-x-2">
                                        <span>Date Created</span>
                                        <button
                                            onClick={() =>
                                                handleSortOrder(
                                                    paramOrderBy ===
                                                        SORT_TYPE.CREATED_ASC
                                                        ? SORT_TYPE.CREATED_DESC
                                                        : SORT_TYPE.CREATED_ASC
                                                )
                                            }
                                            disabled={items.length === 0}
                                        >
                                            {paramOrderBy ===
                                                SORT_TYPE.CREATED_ASC ? (
                                                <TbSortAscending2 />
                                            ) : (
                                                <TbSortDescending2 />
                                            )}
                                        </button>
                                    </label>
                                </th>
                                <th className="">
                                    <label className="flex items-center space-x-2">
                                        <span>Date Updated</span>
                                        <button
                                            onClick={() =>
                                                handleSortOrder(
                                                    paramOrderBy ===
                                                        SORT_TYPE.UPDATED_ASC
                                                        ? SORT_TYPE.UPDATED_DESC
                                                        : SORT_TYPE.UPDATED_ASC
                                                )
                                            }
                                            disabled={items.length === 0}
                                        >
                                            {paramOrderBy ===
                                                SORT_TYPE.UPDATED_ASC ? (
                                                <TbSortAscending2 />
                                            ) : (
                                                <TbSortDescending2 />
                                            )}
                                        </button>
                                    </label>
                                </th>
                                <th className="">
                                    <span className="text-xl">
                                        <MdOutlineSettings />
                                    </span>
                                </th>
                            </tr>
                        </thead>
                        <tbody className="text-sm">
                            {items.map((item, index) => (
                                <WireTransferItemComponent
                                    key={"ci-" + index}
                                    item={item}
                                    isSelected={selectedItems.includes(item.id)}
                                    onCheckboxChange={handleItemCheckboxChange}
                                    handleClickNote={
                                        () => {
                                            setStateSelectedItem(item);
                                            setStateModalActiveNote(true);
                                        }
                                    }
                                />
                            ))}
                        </tbody>
                    </table>
                </div>

                {/* Pagination */}
                <CursorPaginate
                    onFirstPage={onFirstPage}
                    onLastPage={onLastPage}
                    nextPageUrl={nextPageUrl}
                    previousPageUrl={previousPageUrl}
                    itemCount={itemCount}
                    total={total}
                    shouldPreserveState={true}
                />
            </div>
        </AdminLayout>
    );
}
