<?php

namespace App\Modules\MarketPlace\Requests;

use App\Modules\MarketPlace\Constants\AfternicOfferConstants;
use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class OfferUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules() : array
    {
        return [
            'id' => 'required|integer',
            'status' => 'required|string',
            'counter_offer' => 'required|integer',
            'feedback' => 'nullable|string|max:255'
        ];
    }

    public function update() : void
    {
        $date = Carbon::now();

        DB::table('public.afternic_offers')->where('id', $this->id)->update([
            'offer_status' => $this->status,
            'counter_offer_price' => $this->counter_offer,
            'updated_at' => $date,
        ]);

        DB::table('public.afternic_offer_histories')->insert([
            'afternic_offer_id' => $this->id,
            'offer_price' => $this->offer,
            'counter_offer_price' => $this->counter_offer,
            'offer_status' => $this->status,
            'feedback' => $this->feedback ? $this->feedback : '',
            'created_at' => $date,
            'updated_at' => $date,
        ]);
    }
}
